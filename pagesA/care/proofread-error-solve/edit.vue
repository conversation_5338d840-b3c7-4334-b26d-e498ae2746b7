<template>
    <ut-page>
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" title="校对异常处理" navbarType="1"></f-navbar>
        </ut-top>

        <view v-loading="loading" class="edit-container" :style="{paddingTop: topWrapHeight + 'px'}">
            <!-- 客户信息 -->
            <view class="customer-info">
                <view class="info-title">客户信息</view>
                <view class="info-grid">
                    <view class="info-item">
                        <text class="label">客户姓名：</text>
                        <text class="value">{{ form.name }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">性别：</text>
                        <text class="value">
                            <text v-if="form.sex === 1">男</text>
                            <text v-else-if="form.sex === 2">女</text>
                            <text v-else>未知</text>
                        </text>
                    </view>
                    <view class="info-item">
                        <text class="label">手机号：</text>
                        <text class="value">{{ form.phone }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">证件号码：</text>
                        <text class="value">{{ form.idcard }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">分组名称：</text>
                        <text class="value">{{ form.groupName }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">护理员：</text>
                        <text class="value">{{ form.attendantName }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">护理日期：</text>
                        <text class="value">{{ form.workDate }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">排班时长：</text>
                        <text class="value">{{ form.schedulingDuration }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">实际时长：</text>
                        <text class="value">{{ form.totalDuration }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">签到时间：</text>
                        <text class="value">{{ form.checkInTime || '未签到' }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">签退时间：</text>
                        <text class="value">{{ form.checkOutTime || '未签退' }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">校对人：</text>
                        <text class="value">{{ form.lastManualUserName }}</text>
                    </view>
                    <view class="info-item">
                        <text class="label">校对时间：</text>
                        <text class="value">{{ form.lastManualTime }}</text>
                    </view>
                    <view class="info-item full-width">
                        <text class="label">校对异常信息：</text>
                        <text class="value error-text">{{ form.proofreadErrorRemark }}</text>
                    </view>
                </view>
            </view>

            <!-- 服务项目 -->
            <view class="content-section">
                <view class="section-header">
                    <view class="section-title">服务项目</view>
                </view>
                <view v-if="form.projects && form.projects.length > 0" class="project-list">
                    <view v-for="(project, index) in form.projects" :key="index" class="project-item">
                        <view class="project-name">{{ project.name || project.projectName }}</view>
                        <view class="project-info">
                            <text class="project-detail">{{ project.description || '暂无描述' }}</text>
                            <view v-if="project.requireMinDuration || project.requireMaxDuration"
                                class="project-duration">
                                <text class="duration-text">
                                    时长要求：{{ project.requireMinDuration || 0 }}-{{
                                        project.requireMaxDuration || 0
                                    }}分钟
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-else class="empty-state">
                    <text>暂无服务项目</text>
                </view>
            </view>

            <!-- 服务资料 -->
            <view class="content-section">
                <view class="section-header">
                    <view class="section-title">服务资料</view>
                </view>
                <view v-if="datas && datas.length > 0" class="data-list">
                    <view v-for="(data, index) in datas" :key="index" class="data-item">
                        <view class="data-name">{{ data.name || data.title }}</view>
                        <view class="data-info">
                            <text class="data-detail">{{ data.description || '暂无描述' }}</text>
                            <view v-if="data.details && data.details.length > 0" class="data-details">
                                <view v-for="(detail, dIndex) in data.details" :key="dIndex" class="detail-item">
                                    <text class="detail-title">{{ detail.title }}</text>
                                    <text v-if="detail.require" class="detail-require">(必填)</text>
                                    <text v-else class="detail-optional">(选填)</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-else class="empty-state">
                    <text>暂无服务资料</text>
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="footer-buttons">
                <u-button
                    type="primary"
                    text="确定"
                    @click="save"
                    :loading="loading"
                ></u-button>
                <u-button
                    text="返回"
                    @click="goBack"
                ></u-button>
            </view>

        </view>
    </ut-page>
</template>

<script>
import { mapState } from 'vuex'

export default {
    data() {
        return {
            colors: '',
            topWrapHeight: 0,
            workId: '',
            month: '',
            loading: false,
            form: {},
            datas: [],
        }
    },
    computed: {
        ...mapState({
            community: state => state.init.community,
        }),
    },
    onLoad(options) {
        // 获取页面参数
        this.workId = options.workId || ''
        this.month = options.month || ''
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.getInfo()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        async getInfo() {
            if (!this.workId) return
            this.loading = true
            const params = {
                communityId: this.community.id,
                workId: this.workId,
            }
            if (this.month) params.month = this.month
            try {
                const { data } = await this.$ut.api('mang/care/proofread/task', params)
                this.form = {
                    ...this.form,
                    ...data,
                    projects: data.projects?.map((item) => ({
                        ...item,
                        id: item.projectId,
                        _id: item.id,
                    })),
                }
                this.datas = data.datas || []
            } finally {
                this.loading = false
            }
        },
        async save() {
            this.loading = true
            try {
                await this.$ut.api('mang/care/proofread/errorSave', {
                    communityId: this.community.id,
                    workId: this.workId,
                    projectIds: this.form.projects?.map((item) => item.id),
                    datas: this.form.datas,
                })
                this.$u.toast('操作成功')
                this.goBack()
            } finally {
                this.loading = false
            }
        },

        goBack() {
            uni.navigateBack()
        },
    },
}
</script>

<style lang="scss" scoped>
.edit-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.customer-info {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  padding: 10rpx 0;
  display: flex;
  align-items: center;

  &.full-width {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;

    .label {
      margin-bottom: 10rpx;
    }
  }
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;

  &.error-text {
    color: #f56c6c;
    font-weight: bold;
  }
}

.content-section {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.project-list, .data-list {
  padding: 30rpx;

  .project-item, .data-item {
    padding: 25rpx;
    border: 1rpx solid #eee;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .project-name, .data-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
  }

  .project-detail, .data-detail {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }

  .project-duration {
    margin-top: 10rpx;

    .duration-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .data-details {
    margin-top: 15rpx;

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .detail-title {
        font-size: 24rpx;
        color: #666;
        margin-right: 10rpx;
      }

      .detail-require {
        font-size: 22rpx;
        color: #f56c6c;
      }

      .detail-optional {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 30rpx;
  color: #999;
  font-size: 28rpx;
}

.footer-buttons {
  background: white;
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 12rpx;
  display: flex;
  gap: 20rpx;

  .u-button {
    flex: 1;
  }
}


</style>
