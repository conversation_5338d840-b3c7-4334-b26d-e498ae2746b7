<template>
    <ut-page>
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" title="校对异常处理" navbarType="1"></f-navbar>

            <view :style="{backgroundColor:'#fff'}" class="padding-tb-sm padding-lr-sm flex align-center">
                <view class="month-selector-wrapper margin-right-sm">
                    <view class="u-border round padding-tb-sm padding-lr-lg flex align-center justify-between"
                        @click="showMonthPicker = true" style="height: 70rpx">
                        <view class="text-grey text-bold">{{ monthDisplayText }}</view>
                        <view
                            v-if="monthValue"
                            class="clear-btn"
                            @click.stop="clearMonth"
                        >
                            <u-icon name="close" size="14" color="#c0c4cc"></u-icon>
                        </view>
                    </view>
                </view>
                <u-input
                    prefixIcon="search"
                    placeholder="输入客户或护理员姓名"
                    v-model="search.key"
                    shape='circle'
                    border="surround"
                    clearable
                >
                    <template slot="suffix">
                        <u-button
                            text="搜索"
                            type="success"
                            size="mini"
                            @click="onSearch"
                        ></u-button>
                    </template>
                </u-input>
            </view>


        </ut-top>

        <mescroll-body
            ref="mescrollRef"
            :top="topWrapHeight+'px'"
            :top-margin="-topWrapHeight+'px'"
            bottom="20"
            :up="upOption"
            :safearea="true"
            @init="mescrollInit"
            @down="downCallback"
            @up="upCallback"
            @emptyclick="emptyClick"
        >
            <view v-for="(item, index) in dataList" :key="index" @click="goToEdit(item)">
                <view class="item-box">
                    <view class="head-image">
                        <text class="cuIcon-people"></text>
                    </view>
                    <view class="content text-content">
                        <view class="flex justify-between align-center">
                            <view>
                                <view class="flex justify-start align-center flex-wrap">
                                    <view class="text-df text-bold margin-right-xs">{{ item.name }}</view>
                                    <view class="cu-tag sm radius" :class="getStatusClass(item)">
                                        {{ getStatusText(item) }}
                                    </view>

                                </view>
                                <view class="text-sm text-gray margin-top-xs">
                                    护理日期：{{ item.workDate }}
                                </view>
                            </view>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            分组：{{ item.groupName }}
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            护理员：{{ item.attendantName }}
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            手机号：{{ item.phone }}
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            签到时间：
                            <text v-if="item.checkInTime" class="text-black">{{ item.checkInTime }}</text>
                            <text v-else class="text-red">未签到</text>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            签退时间：
                            <text v-if="item.checkOutTime" class="text-black">{{ item.checkOutTime }}</text>
                            <text v-else class="text-red">未签退</text>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            项目数量：
                            <text v-if="item.projectCount && item.projectCount > 0" class="text-black">{{ item.projectCount }}</text>
                            <text v-else class="text-red">无</text>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            文件数量：
                            <text v-if="item.dataCount && item.dataCount > 0" class="text-black">{{ item.dataCount }}</text>
                            <text v-else class="text-red">无</text>
                        </view>

                        <template v-if="item.isManual">
                            <view class="text-sm text-gray margin-top-xs">
                                校对人：{{ item.lastManualUserName }}
                            </view>
                            <view class="text-sm text-gray margin-top-xs">
                                校对时间：{{ item.lastManualTime }}
                            </view>
                            <view class="text-sm margin-top-xs">
                                校对信息：
                                <text v-if="item.proofreadError" class="text-red">{{ item.proofreadErrorRemark }}</text>
                                <text v-else class="text-green">无异常</text>
                            </view>
                        </template>
                    </view>
                </view>
            </view>
        </mescroll-body>

        <u-datetime-picker
            :show="showMonthPicker"
            v-model="monthValue"
            mode="year-month"
            title="选择月份"
            :closeOnClickOverlay="true"
            @confirm="onMonthConfirm"
            @close="showMonthPicker = false"
            @cancel="showMonthPicker = false"
        ></u-datetime-picker>


    </ut-page>
</template>

<script>
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'

const app = getApp()

export default {
    mixins: [MescrollMixin],
    components: {
        MescrollBody,
    },
    props: {
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            colors: '',
            mescroll: null,
            showMonthPicker: false,
            monthValue: null,
            search: {
                key: '',
            },
            topWrapHeight: 0,
            upOption: {
                page: {
                    num: 0,
                    size: 10,
                },
                noMoreSize: 3,
                textNoMore: '没有更多数据了',
                empty: {
                    tip: '暂无校对异常数据',
                },
            },
            pageReq: {
                pageindex: 1,
                pagesize: 10,
            },
            dataList: [],
            firstLoad: true,
        }
    },
    computed: {
        ...mapState({
            commKey: state => state.init.template.commKey,
            token: state => state.user.token,
            userInfo: state => state.user.info,
            community: state => state.init.community,
        }),
        monthDisplayText() {
            if (!this.monthValue) return '选择月份'
            const date = new Date(this.monthValue)
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0')
            return `${year}-${month}`
        },
        month() {
            if (!this.monthValue) return null
            const date = new Date(this.monthValue)
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0')
            return `${year}-${month}`
        },
    },
    mounted() {
        this.downCallback()
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        mescrollInit(mescroll) {
            this.mescroll = mescroll
        },
        downCallback() {
            this.pageReq.pageindex = 1
            this.mescroll.resetUpScroll()
        },
        async upCallback(page) {
            const params = {
                communityId: this.community.id,
                pageindex: page.num,
                pagesize: page.size,
                ...this.search,
            }
            if (this.month) {
                params.month = this.month
            }
            const { data } = await this.$ut.api('mang/care/proofread/errorListpg', params)
            setTimeout(() => {
                this.mescroll.endBySize(data.info.length, data.record)
            }, this.firstLoad ? 0 : 500)
            this.firstLoad = false
            if (page.num === 1) {
                this.dataList = []
            }
            this.dataList = this.dataList.concat(data.info)
        },
        emptyClick() {
            this.mescroll.resetUpScroll()
        },
        onSearch() {
            this.downCallback()
        },
        onClear() {
            this.search.key = ''
            this.downCallback()
        },

        onMonthConfirm({ value }) {
            this.monthValue = value
            this.showMonthPicker = false
            this.downCallback()
        },
        clearMonth() {
            this.monthValue = null
            this.downCallback()
        },
        getStatusClass(item) {
            if (item.proofreadError) {
                return 'bg-orange light'
            }
            return item.isManual ? 'bg-green light' : 'bg-blue light'
        },
        getStatusText(item) {
            if (item.proofreadError) {
                return '异常'
            }
            return item.isManual ? '已校对' : '待校对'
        },
        goToEdit(item) {
            const params = {
                workId: item.id,
            }
            if (this.month) {
                params.month = this.month
            }
            this.$tools.routerTo('/pagesA/care/proofread-error-solve/edit', params)
        },
    },
}
</script>

<style lang="scss" scoped>

.month-selector-wrapper {
  .clear-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28rpx;
    height: 28rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    margin-left: 10rpx;

    &:active {
      background-color: #e0e0e0;
    }
  }
}

.item-box {
  padding: 20rpx 30rpx;
  background-color: white;
  margin-bottom: 10rpx;
  display: flex;
  position: relative;
  overflow: hidden;

  .content {
    flex: 1;
    padding: 0 20rpx;
  }

  &:active {
    transform: scale(0.98);
    transition: all 0.3s ease;
  }
}

.cuIcon-people {
  font-size: 116rpx;
  color: gray;
  border: 1rpx solid #ccc;
  width: 116rpx;
  height: 156rpx;
  line-height: 156rpx;
  border-radius: 6rpx;
  display: block;
}

.modal-content {
  width: 90vw;
  max-width: 800rpx;
  max-height: 80vh;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
}
</style>
